# Guía de Autenticación Híbrida con Firebase

Esta guía explica cómo usar la nueva funcionalidad de autenticación híbrida que soporta tanto usuarios locales como autenticación con Google via Firebase.

## Configuración Inicial

### 1. Variables de Entorno

Asegúrate de configurar las siguientes variables en tu archivo `.env`:

```env
# JWT Configuration
JWT_SECRET="your_jwt_secret_key_here"
JWT_EXPIRES_IN="7d"

# Firebase Admin SDK Configuration
FIREBASE_PROJECT_ID="your_firebase_project_id"
FIREBASE_PRIVATE_KEY_ID="your_private_key_id"
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour_private_key_here\n-----END PRIVATE KEY-----"
FIREBASE_CLIENT_EMAIL="firebase-adminsdk-xxxxx@your_project.iam.gserviceaccount.com"
FIREBASE_CLIENT_ID="your_client_id"
FIREBASE_CLIENT_X509_CERT_URL="https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your_project.iam.gserviceaccount.com"
```

### 2. Obtener Credenciales de Firebase

1. Ve a la [Consola de Firebase](https://console.firebase.google.com/)
2. Selecciona tu proyecto
3. Ve a Configuración del proyecto > Cuentas de servicio
4. Haz clic en "Generar nueva clave privada"
5. Descarga el archivo JSON y extrae los valores para las variables de entorno

## Endpoints de Autenticación

### 1. Login Híbrido

**Endpoint:** `POST /api/auth/login`

#### Autenticación Local
```json
{
  "email": "<EMAIL>",
  "password": "contraseña123"
}
```

#### Autenticación con Firebase
```json
{
  "firebaseToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjE2NzAyN..."
}
```

**Respuesta exitosa:**
```json
{
  "message": "Login successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "firstname": "Juan",
    "lastname": "Pérez",
    "email": "<EMAIL>",
    "role": "USER",
    "provider": "google",
    "avatarUrl": "https://lh3.googleusercontent.com/..."
  }
}
```

### 2. Registro de Usuario Local

**Endpoint:** `POST /api/auth/register`

```json
{
  "firstname": "Juan",
  "lastname": "Pérez",
  "email": "<EMAIL>",
  "password": "contraseña123"
}
```

**Respuesta exitosa:**
```json
{
  "message": "User registered successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "firstname": "Juan",
    "lastname": "Pérez",
    "email": "<EMAIL>",
    "role": "USER"
  }
}
```

### 3. Autenticación con Google (Login/Registro Automático)

**Endpoint:** `POST /api/auth/google`

```json
{
  "idToken": "eyJhbGciOiJSUzI1NiIsImtpZCI6IjE2NzAyN..."
}
```

**Respuesta exitosa (Usuario nuevo - Registro):**
```json
{
  "message": "User registered and logged in successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "firstname": "Juan",
    "lastname": "Pérez",
    "email": "<EMAIL>",
    "role": "USER",
    "provider": "google",
    "avatarUrl": "https://lh3.googleusercontent.com/...",
    "emailVerified": true
  }
}
```

**Respuesta exitosa (Usuario existente - Login):**
```json
{
  "message": "Login successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "firstname": "Juan",
    "lastname": "Pérez",
    "email": "<EMAIL>",
    "role": "USER",
    "provider": "google",
    "avatarUrl": "https://lh3.googleusercontent.com/...",
    "emailVerified": true
  }
}
```

### 4. Renovar Token de Acceso

**Endpoint:** `POST /api/auth/refresh`

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Respuesta exitosa:**
```json
{
  "message": "Token refreshed successfully",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## Flujo de Autenticación

### Para Usuarios Locales

1. El usuario ingresa email y contraseña
2. El frontend envía una petición POST a `/api/auth/login` con email y password
3. El backend verifica las credenciales contra la base de datos
4. Si son válidas, retorna un JWT token

### Para Usuarios de Google

1. El usuario se autentica con Google en el frontend usando Firebase Auth
2. Firebase retorna un ID token
3. El frontend envía el token a `/api/auth/google` con el campo `idToken`
4. El backend verifica el token con Firebase Admin SDK
5. Si es válido, busca al usuario en la base de datos
6. Si existe, actualiza la información del perfil y retorna un JWT token (Login)
7. Si no existe, crea un nuevo usuario y retorna un JWT token (Registro automático)

## Manejo de Errores

### Errores Comunes

- **401 Unauthorized**: Token inválido o credenciales incorrectas
- **400 Bad Request**: Datos faltantes o inválidos
- **404 Not Found**: Usuario no encontrado (para login con Firebase)
- **409 Conflict**: Usuario ya existe (para registro)

### Ejemplos de Respuestas de Error

```json
{
  "message": "Invalid Firebase token"
}
```

```json
{
  "message": "This email is associated with a local account. Please use email and password to login."
}
```

## Consideraciones de Seguridad

1. **Tokens JWT**:
   - **Access Token**: Expira en 15 minutos para mayor seguridad
   - **Refresh Token**: Expira en 7 días y se usa para renovar access tokens
   - Ambos tokens se regeneran en cada renovación
2. **Firebase Tokens**: Se verifican en tiempo real con Firebase Admin SDK
3. **Passwords**: Se hashean usando bcrypt con salt de 10 rounds
4. **Separación de Proveedores**: Los usuarios locales no pueden usar Google login y viceversa
5. **Renovación de Tokens**: El refresh token debe almacenarse de forma segura en el cliente

## Testing

Para probar la funcionalidad, puedes usar herramientas como Postman o curl:

```bash
# Login local
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Autenticación con Google (Login/Registro automático)
curl -X POST http://localhost:3001/api/auth/google \
  -H "Content-Type: application/json" \
  -d '{"idToken":"your_firebase_id_token_here"}'

# Renovar token de acceso
curl -X POST http://localhost:3001/api/auth/refresh \
  -H "Content-Type: application/json" \
  -d '{"refreshToken":"your_refresh_token_here"}'
```
