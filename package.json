{"name": "taxia-core", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@prisma/client": "^6.8.2", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "firebase-admin": "^13.4.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "prisma": "^6.8.2", "winston": "^3.17.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.18", "@types/express": "^4.17.23", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.9", "@types/node": "^22.15.18", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}