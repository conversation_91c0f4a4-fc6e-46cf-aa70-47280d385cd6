import { Request, Response, NextFunction } from 'express';
import { prisma } from '../index';
import { logger } from '../utils/logger';

/**
 * Get all orders
 * @route GET /api/orders
 */
export const getAllOrders = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const orders = await prisma.order.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json({
      success: true,
      count: orders.length,
      data: orders
    });
    return;
  } catch (error) {
    logger.error('Error in getAllOrders controller:', error);
    next(error);
  }
};

/**
 * Get current user's orders
 * @route GET /api/orders/user
 */
export const getUserOrders = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = (req as any).user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    const orders = await prisma.order.findMany({
      where: {
        userId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.status(200).json({
      success: true,
      count: orders.length,
      data: orders
    });
    return;
  } catch (error) {
    logger.error('Error in getUserOrders controller:', error);
    next(error);
  }
};

/**
 * Get order by ID
 * @route GET /api/orders/:id
 */
export const getOrderById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const userRole = (req as any).user?.role;

    const order = await prisma.order.findUnique({
      where: { id }
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if user is authorized to view this order
    if (userRole !== 'ADMIN' && order.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this order'
      });
    }

    res.status(200).json({
      success: true,
      data: order
    });
    return;
  } catch (error) {
    logger.error('Error in getOrderById controller:', error);
    next(error);
  }
};

/**
 * Create a new order
 * @route POST /api/orders
 */
export const createOrder = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { items, total } = req.body;
    const userId = (req as any).user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'User not authenticated'
      });
    }

    // Validate items
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Order must contain at least one item'
      });
    }

    // Create order
    const order = await prisma.order.create({
      data: {
        userId,
        total: parseFloat(total),
        items: items as any,
        status: 'PENDING'
      }
    });

    res.status(201).json({
      success: true,
      data: order
    });
    return;
  } catch (error) {
    logger.error('Error in createOrder controller:', error);
    next(error);
  }
};

/**
 * Update order
 * @route PUT /api/orders/:id
 */
export const updateOrder = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    // Check if order exists
    const orderExists = await prisma.order.findUnique({
      where: { id }
    });

    if (!orderExists) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Update order
    const updatedOrder = await prisma.order.update({
      where: { id },
      data: { status }
    });

    res.status(200).json({
      success: true,
      data: updatedOrder
    });
    return;
  } catch (error) {
    logger.error('Error in updateOrder controller:', error);
    next(error);
  }
};

/**
 * Delete order
 * @route DELETE /api/orders/:id
 */
export const deleteOrder = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    // Check if order exists
    const orderExists = await prisma.order.findUnique({
      where: { id }
    });

    if (!orderExists) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Delete order
    await prisma.order.delete({
      where: { id }
    });

    res.status(200).json({
      success: true,
      message: 'Order deleted successfully'
    });
    return;
  } catch (error) {
    logger.error('Error in deleteOrder controller:', error);
    next(error);
  }
};
