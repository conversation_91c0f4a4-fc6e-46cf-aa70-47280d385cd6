// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// MODELOS BASE - AUTENTICACIÓN Y USUARIOS
// ============================================================================

model User {
  id            String   @id @default(uuid())
  username      String?  @unique
  email         String   @unique
  emailVerified Boolean  @default(false)
  password      String?
  avatarUrl     String?
  firstname     String?
  lastname      String?
  phone         String?
  provider      String   @default("local") // 'local' o 'google'
  providerId    String?  // UID de Firebase
  role          String   @default("USER")
  taxpayerId    String?  @unique
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relación con Taxpayer
  taxpayer Taxpayer? @relation(fields: [taxpayerId], references: [id])

  @@map("users")
}

model Taxpayer {
  id               String    @id @default(uuid())
  rif              String    @unique
  name             String
  licenseNumber    String    @unique
  email            String
  legalResponsible String?
  localPhone       String?
  phone            String
  address          String
  state            String
  municipality     String
  latitude         Float?
  longitude        Float?
  locationAddress  String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt

  // Relaciones
  users                        User[]
  economicActivityDeclarations EconomicActivityDeclaration[]
  urbanRealEstateDeclarations  UrbanRealEstateDeclaration[]
  vehicleDeclarations          VehicleDeclaration[]
  legalBetDeclarations         LegalBetDeclaration[]
  advertisingDeclarations      AdvertisingDeclaration[]
  showDeclarations             ShowDeclaration[]
  taxRateDeclarations          TaxRateDeclaration[]
  urbanSanitationDeclarations  UrbanSanitationDeclaration[]

  @@map("taxpayers")
}

// ============================================================================
// MODELOS DE DECLARACIONES - ACTIVIDADES ECONÓMICAS
// ============================================================================

model EconomicActivityDeclaration {
  id               String            @id @default(uuid())
  taxpayerId       String
  declarationType  String // 'definitiva' | 'sustitutiva' | 'anticipada'
  year             Int
  month            Int
  total            Float
  submissionDate   DateTime          @default(now())
  status           String            @default("pendiente") // 'enviada' | 'pendiente' | 'rechazada' | 'aprobada' | 'pagado'
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt

  // Relaciones
  taxpayer   Taxpayer                @relation(fields: [taxpayerId], references: [id])
  activities EconomicActivityEntry[]

  @@map("economic_activity_declarations")
}

model EconomicActivityEntry {
  id            String  @id @default(uuid())
  declarationId String
  number        String
  code          String
  description   String
  aliquot       String
  mmv           Float
  createdAt     DateTime @default(now())

  // Relaciones
  declaration EconomicActivityDeclaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("economic_activity_entries")
}

// ============================================================================
// MODELOS DE DECLARACIONES - INMUEBLES URBANOS
// ============================================================================

model UrbanRealEstateDeclaration {
  id                      String   @id @default(uuid())
  taxpayerId              String
  location                String
  state                   String
  municipality            String
  cadastralId             String
  area                    Float
  propertyType            String
  propertyTypeName        String
  description             String
  usage                   String
  landValuePlan           String
  constructionValuePlan   String
  constructionType        String
  zone                    String
  landArea                Float
  constructionArea        Float
  tcmdM2                  Float
  exchangeRateType        String
  aliquot                 Float
  taxAmount               Float
  period                  String
  year                    Int
  submissionDate          DateTime @default(now())
  status                  String   @default("pendiente")
  createdAt               DateTime @default(now())
  updatedAt               DateTime @updatedAt

  // Relaciones
  taxpayer   Taxpayer              @relation(fields: [taxpayerId], references: [id])
  properties PropertyEntry[]

  @@map("urban_real_estate_declarations")
}

model PropertyEntry {
  id                    String  @id @default(uuid())
  declarationId         String
  propertyType          String
  propertyTypeName      String
  description           String
  usage                 String
  sectorClassification  String
  landValuePlan         String
  landArea              Float
  constructionArea      Float
  tcmdM2                Float
  exchangeRateType      String
  aliquot               Float
  taxAmount             Float
  createdAt             DateTime @default(now())

  // Relaciones
  declaration UrbanRealEstateDeclaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("property_entries")
}

// ============================================================================
// MODELOS DE DECLARACIONES - VEHÍCULOS
// ============================================================================

model VehicleDeclaration {
  id               String   @id @default(uuid())
  taxpayerId       String
  plateNumber      String
  brand            String
  model            String
  year             Int
  vehicleType      String
  ownerName        String
  taxAmount        Float
  period           String
  submissionDate   DateTime @default(now())
  status           String   @default("pendiente")
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relaciones
  taxpayer Taxpayer       @relation(fields: [taxpayerId], references: [id])
  vehicles VehicleEntry[]

  @@map("vehicle_declarations")
}

model VehicleEntry {
  id            String @id @default(uuid())
  declarationId String
  plateNumber   String
  brand         String
  model         String
  year          Int
  vehicleType   String
  engineNumber  String
  chassisNumber String
  color         String
  createdAt     DateTime @default(now())

  // Relaciones
  declaration VehicleDeclaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("vehicle_entries")
}

// ============================================================================
// MODELOS DE DECLARACIONES - APUESTAS LÍCITAS
// ============================================================================

model LegalBetDeclaration {
  id             String   @id @default(uuid())
  taxpayerId     String
  betType        String
  numberOfBets   Int
  betAmount      Float
  taxRate        String
  total          Float
  month          Int
  year           Int
  submissionDate DateTime @default(now())
  status         String   @default("pendiente")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relaciones
  taxpayer Taxpayer   @relation(fields: [taxpayerId], references: [id])
  entries  BetEntry[]

  @@map("legal_bet_declarations")
}

model BetEntry {
  id              String @id @default(uuid())
  declarationId   String
  betType         String
  betTypeName     String
  numberOfBets    Int
  betAmount       Float
  taxRate         String
  taxAmount       Float
  declarationType String
  month           Int
  year            Int
  createdAt       DateTime @default(now())

  // Relaciones
  declaration LegalBetDeclaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("bet_entries")
}

// ============================================================================
// MODELOS DE DECLARACIONES - PUBLICIDAD Y PROPAGANDA
// ============================================================================

model AdvertisingDeclaration {
  id             String   @id @default(uuid())
  taxpayerId     String
  advertisingType String
  description    String
  total          Float
  month          Int
  year           Int
  submissionDate DateTime @default(now())
  status         String   @default("pendiente")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relaciones
  taxpayer Taxpayer           @relation(fields: [taxpayerId], references: [id])
  entries  AdvertisingEntry[]

  @@map("advertising_declarations")
}

model AdvertisingEntry {
  id               String @id @default(uuid())
  declarationId    String
  type             String
  exactAddress     String
  area             Float
  numberOfElements Int
  numberOfDays     Int
  fraction         Float
  exchangeType     Float
  description      String
  location         String?
  dimensions       String?
  duration         Int?
  taxAmount        Float
  month            Int
  year             Int
  createdAt        DateTime @default(now())

  // Relaciones
  declaration AdvertisingDeclaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("advertising_entries")
}

// ============================================================================
// MODELOS DE DECLARACIONES - ESPECTÁCULOS PÚBLICOS
// ============================================================================

model ShowDeclaration {
  id             String   @id @default(uuid())
  taxpayerId     String
  showType       String
  showName       String
  venue          String
  eventDate      String
  capacity       Int
  ticketPrice    Float
  taxRate        String
  total          Float
  month          Int
  year           Int
  submissionDate DateTime @default(now())
  status         String   @default("pendiente")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relaciones
  taxpayer Taxpayer    @relation(fields: [taxpayerId], references: [id])
  entries  ShowEntry[]

  @@map("show_declarations")
}

model ShowEntry {
  id              String @id @default(uuid())
  declarationId   String
  showType        String
  showTypeName    String
  showName        String
  venue           String
  eventDate       String
  capacity        Int
  ticketPrice     Float
  taxRate         String
  taxAmount       Float
  declarationType String
  month           Int
  year            Int
  createdAt       DateTime @default(now())

  // Relaciones
  declaration ShowDeclaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("show_entries")
}

// ============================================================================
// MODELOS DE DECLARACIONES - TASAS MUNICIPALES
// ============================================================================

model TaxRateDeclaration {
  id               String   @id @default(uuid())
  taxpayerId       String
  total            Float
  month            Int
  year             Int
  submissionDate   DateTime @default(now())
  status           String   @default("pendiente")
  numberOfTaxRates Int
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relaciones
  taxpayer        Taxpayer        @relation(fields: [taxpayerId], references: [id])
  entries         TaxRateEntry[]
  selectedTaxRates TaxRateItem[]

  @@map("tax_rate_declarations")
}

model TaxRateEntry {
  id              String @id @default(uuid())
  declarationId   String
  taxType         String
  taxTypeName     String
  description     String
  amount          Float
  taxRate         String
  taxAmount       Float
  declarationType String
  month           Int
  year            Int
  createdAt       DateTime @default(now())

  // Relaciones
  declaration TaxRateDeclaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("tax_rate_entries")
}

model TaxRateItem {
  id                  String @id @default(uuid())
  declarationId       String
  taxType             String
  description         String
  calculationVariable String
  unitValue           Float
  quantity            Int
  totalAmount         Float
  isSelected          Boolean
  createdAt           DateTime @default(now())

  // Relaciones
  declaration TaxRateDeclaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("tax_rate_items")
}

// ============================================================================
// MODELOS DE DECLARACIONES - ASEO URBANO
// ============================================================================

model UrbanSanitationDeclaration {
  id             String   @id @default(uuid())
  taxpayerId     String
  category       String
  zone           String
  total          Float
  month          Int
  year           Int
  submissionDate DateTime @default(now())
  status         String   @default("pendiente")
  taxpayer       String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relaciones
  taxpayerRef  Taxpayer           @relation(fields: [taxpayerId], references: [id])
  entries      SanitationEntry[]
  propertyInfo PropertyInfo?

  @@map("urban_sanitation_declarations")
}

model SanitationEntry {
  id              String @id @default(uuid())
  declarationId   String
  taxpayerType    String
  serviceType     String
  serviceTypeName String
  frequency       String
  wasteVolume     Float
  taxRate         String
  taxAmount       Float
  declarationType String
  month           Int
  year            Int
  createdAt       DateTime @default(now())

  // Relaciones
  declaration UrbanSanitationDeclaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("sanitation_entries")
}

model PropertyInfo {
  id             String @id @default(uuid())
  declarationId  String @unique
  location       String
  cadastralCode  String
  terrainArea    Float
  paymentYear    Int
  paymentMonth   Int
  taxpayerType   String
  licenseNumber  String
  parish         String
  zone           String
  category       String
  mmvAmount      Float
  createdAt      DateTime @default(now())

  // Relaciones
  declaration UrbanSanitationDeclaration @relation(fields: [declarationId], references: [id], onDelete: Cascade)

  @@map("property_info")
}