// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

//Usuario
model User {
  id                                String                              @id @default(uuid())
  email                             String                              @unique
  password                          String?
  username                          String?
  iduser                            String?
  firstname                         String?
  lastname                          String?
  localphone                        String?
  phone                             String?
  role                              Role                             @default(USER)
  taxpayer                          Taxpayer?
  provider                          String   @default("local") // 'local' o 'google'
  providerId                        String?  // UID de Firebase
  emailVerified                     Boolean  @default(false)
  avatarUrl                         String?  // Para guardar la foto del perfil si viene de Google
  createdAt                         DateTime                            @default(now())
  updatedAt                         DateTime                            @updatedAt
  EconomicActivity                  EconomicActivity[]
  EconomicActivityTaxpayer          EconomicActivityTaxpayer[]
  DeclarationEconomicActivity       DeclarationEconomicActivity[]
  DeclarationEconomicActivityDetail DeclarationEconomicActivityDetail[]
  UrbanProperty                     UrbanProperty[]
  DeclarationUrbanProperty          DeclarationUrbanProperty[]
  Vehicle                           Vehicle[]
  DeclarationVehicle                DeclarationVehicle[]
}

//Contribuyente
model Taxpayer {
  id                                String                              @id @default(uuid())
  taxId                             String                              @unique
  email                             String                              @unique
  name                              String
  iduser                            String
  licenseNumber                     String
  legalResponsible                  String?
  localPhone                        String?
  phone                             String
  address                           String
  municipality                      String
  state                             String
  country                           String
  zipCode                           String?
  location                          Json?
  createdAt                         DateTime                            @default(now())
  updatedAt                         DateTime                            @updatedAt
  EconomicActivityTaxpayer          EconomicActivityTaxpayer[]
  DeclarationEconomicActivity       DeclarationEconomicActivity[]
  DeclarationEconomicActivityDetail DeclarationEconomicActivityDetail[]
  UrbanProperty                     UrbanProperty[]
  DeclarationUrbanProperty          DeclarationUrbanProperty[]
  Vehicle                           Vehicle[]
  DeclarationVehicle                DeclarationVehicle[]
  User                              User? @relation(fields: [iduser], references: [id])
}

// Forma de pago
model PaymentForm {
  id            String   @id @default(uuid())
  iduser        String
  taxId      String
  dateTime      DateTime
  typePayment   String
  accountNumber String
  incomeAccount String
  idTaxpayer    String
  total         Float
  status        Int
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
}

// Detalle de la forma de pago
model PaymentFormDetail {
  id         String   @id @default(uuid())
  iduser     String
  taxId   String
  dateTime   DateTime
  concept    String
  amount     Float
  amountDebt Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
}

// Impuestos Administrativos
model Taxes {
  id          String   @id @default(uuid())
  name        String
  description String?
  percentage  Float
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

//Base Legal
model TypeBaseLaw {
  id          String   @id @default(uuid())
  name        String
  description String?
  documentUrl String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

//Tipo de documento
model TypeDocument {
  id        String   @id @default(uuid())
  name      String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Tipo de persona
model TypePerson {
  id        String   @id @default(uuid())
  name      String
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Actividades económicas
model EconomicActivity {
  id                       String                     @id @default(uuid())
  code                     String //codigo de la actividad económica
  name                     String //nombre de la actividad económica
  description              String? //descripcion de la actividad económica
  aliquot                  Float //alícuota de la actividad económica
  minimumTaxMMV            Float //impuesto mínimo de la actividad económica
  status                   String // Pendiente, En progreso, Completado, etc.
  userId                   String // Relación con el usuario
  User                     User                       @relation(fields: [userId], references: [id]) // Relación con el usuario
  isActive                 Boolean                    @default(true)
  createdAt                DateTime                   @default(now())
  updatedAt                DateTime                   @updatedAt
  EconomicActivityTaxpayer EconomicActivityTaxpayer[]
}

// Actividades económicas del Contribuyente
model EconomicActivityTaxpayer {
  id                                String                              @id @default(uuid())
  taxpayerId                        String // Relación con el contribuyente
  taxpayer                          Taxpayer                            @relation(fields: [taxpayerId], references: [id]) // Relación con el contribuyente
  economicActivityId                String // Relación con la actividad económica
  economicActivity                  EconomicActivity                    @relation(fields: [economicActivityId], references: [id]) // Relación con la actividad económica
  aliquot                           Float //alícuota de la actividad económica
  minimumTaxMMV                     Float //impuesto mínimo de la actividad económica
  licenseNumber                     String?
  licenseDate                       DateTime?
  licenseExpirationDate             DateTime?
  workWithoutLicense                Boolean                             @default(false) //permitir trabajar sin licencia 
  //Usuario que otorga la licencia
  userId                            String // Relación con el usuario
  User                              User                                @relation(fields: [userId], references: [id]) // Relación con el usuario
  status                            String // Pendiente, En progreso, Completado, etc.
  isActive                          Boolean                             @default(true)
  createdAt                         DateTime                            @default(now())
  updatedAt                         DateTime                            @updatedAt
  DeclarationEconomicActivityDetail DeclarationEconomicActivityDetail[]
}

//declaraciones Actividades económicas
model DeclarationEconomicActivity {
  id              String    @id @default(uuid())
  TaxpayerId      String // Relación con el contribuyente
  taxpayer        Taxpayer  @relation(fields: [TaxpayerId], references: [id]) // Relación con el contribuyente
  declarationDate DateTime? // Fecha de la declaración
  paymentDeadline DateTime? // Fecha limite de pago
  declarationType String // Definitiva, Sustitutiva, etc.
  municipality    String? //Municipio donde se realizo la actividad
  userId          String // Relación con el usuario
  User            User      @relation(fields: [userId], references: [id]) // Relación con el usuario
  status          String // Pendiente, En progreso, Completado, etc.
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
}

//declaraciones Actividades económicas detalle
model DeclarationEconomicActivityDetail {
  id                        String                   @id @default(uuid())
  TaxpayerId                String // Relación con el contribuyente
  taxpayer                  Taxpayer                 @relation(fields: [TaxpayerId], references: [id]) // Relación con el contribuyente
  economicActivityId        String // Relación con la actividad económica
  economicActivity          EconomicActivityTaxpayer @relation(fields: [economicActivityId], references: [id])
  baseAmount                Float //Monto base de la actividad
  taxableAmount             Float //Monto imponible de la actividad
  taxRate                   Float //Tasa de impuesto
  calculatedTax             Float //Impuesto calculado
  uploadedIvaFileUrl        String? // Ruta al archivo IVA del periodo
  uploadedIvaRelationUrl    String? // Ruta al archivo relación de ingresos
  totalGrossIncome          Float // Ingresos brutos totales
  incomeOutsideMunicipality Float? //Ingreso fuera del Municipio
  incomeOutsideState        Float? //Ingreso fuera del Estado
  incomeInsideMunicipality  Float? //Ingresos dentro del Municipio
  licenseNumber             String?
  licenseDate               DateTime?
  licenseExpirationDate     DateTime?
  userId                    String // Relación con el usuario
  User                      User                     @relation(fields: [userId], references: [id]) // Relación con el usuario
  status                    String // Pendiente, En progreso, Completado, etc.
  isActive                  Boolean                  @default(true)
  createdAt                 DateTime                 @default(now())
  updatedAt                 DateTime                 @updatedAt
}

//  Inmuebles Urbanos
model UrbanProperty {
  id                        String   @id @default(uuid())
  taxpayerId                String // Relación con el contribuyente
  taxpayer                  Taxpayer @relation(fields: [taxpayerId], references: [id]) // Relación con el contribuyente    
  description               String? // Descripción del inmueble
  address                   String? // Dirección del inmueble
  municipality              String? // Municipio donde se encuentra el inmueble
  state                     String? // Estado donde se encuentra el inmueble
  country                   String? // País donde se encuentra el inmueble
  zipCode                   String? // Código postal del inmueble
  useType                   String? // Uso del inmueble
  plantType                 String? // Tipo de Planta de Valores (Tierra, Construcción)
  sector                    String? // Sector del inmueble
  landArea                  Float? //Area del Terreno
  builtArea                 Float? //Area de construcción
  landClasification         String? // Planta de Valores de la tierra 
  constructionClasification String? // Planta de Valores de la Construcción
  documentUrl               String? // Url documento de propiedad
  propertyValue             Float? // Valor del inmueble
  propertyAge               Int? // Edad del inmueble
  status                    String // Pendiente, En progreso, Completado, etc.
  userId                    String // Relación con el usuario
  User                      User     @relation(fields: [userId], references: [id]) // Relación con el usuario
  isActive                  Boolean  @default(true)
  createdAt                 DateTime @default(now())
  updatedAt                 DateTime @updatedAt

  DeclarationUrbanPropertyDetail DeclarationUrbanPropertyDetail[]
}

// Declaración de Inmuebles Urbanos
model DeclarationUrbanProperty {
  id                       String    @id @default(uuid())
  taxpayerId               String // Relación con el contribuyente
  taxpayer                 Taxpayer  @relation(fields: [taxpayerId], references: [id]) // Relación con el contribuyente
  doc                      String // Correlativo de la declaración
  declarationDate          DateTime? // Fecha de la declaración
  paymentDeadline          DateTime? // Fecha limite de pago
  propertyAreaLand         Float? // Área de Terreno del inmueble
  propertyAreaConstruction Float? // Área de Construcción del inmueble
  totalTax                 Float? // Impuesto total
  userId                   String // Relación con el usuario
  User                     User      @relation(fields: [userId], references: [id]) // Relación con el usuario
  status                   String // Pendiente, En progreso, Completado, etc.
  isActive                 Boolean   @default(true)
  createdAt                DateTime  @default(now())
  updatedAt                DateTime  @updatedAt

  DeclarationUrbanPropertyDetail DeclarationUrbanPropertyDetail[]
}

// Declaración de Inmuebles Urbanos Detalles
model DeclarationUrbanPropertyDetail {
  id                        String                   @id @default(uuid())
  docId                     String // Relación con la declaración
  doc                       DeclarationUrbanProperty @relation(fields: [docId], references: [id]) // Correlativo de la declaración
  item                      Int? // Item de la declaración
  urbanPropertyId           String // Relación con el inmueble
  urbanProperty             UrbanProperty            @relation(fields: [urbanPropertyId], references: [id]) // Relación con el inmueble
  declarationDate           DateTime? // Fecha de la declaración
  paymentDeadline           DateTime? // Fecha limite de pago
  useType                   String? // Uso del inmueble
  plantType                 String? // Tipo de Planta de Valores (Tierra, Construcción, etc.)
  landClasification         String? // Planta de Valores de la tierra 
  constructionClasification String? // Planta de Valores de la Construcción
  propertyAreaConstruction  Float? // Área de Construcción del inmueble
  Area                      Float? // Área de Terreno del inmueble
  tcmd                      Float? // TCMD del inmueble
  typeMMV                   Float? // Tipo de MMV del inmueble
  aliquot                   Float? // Alícuota del inmueble
  totalTax                  Float? // Impuesto total
  status                    String // Pendiente, En progreso, Completado, etc.
  isActive                  Boolean                  @default(true)
  createdAt                 DateTime                 @default(now())
  updatedAt                 DateTime                 @updatedAt
}

model Vehicle {
  id                 String               @id @default(uuid())
  taxpayerId         String // Relación con el contribuyente
  taxpayer           Taxpayer             @relation(fields: [taxpayerId], references: [id]) // Relación con el contribuyente
  contributorSince   DateTime? // Fecha de contribución
  vehicleUseType     String // Uso del vehículo
  vehicleType        String // Particular, Comercial, etc.  
  seats              Int? // Número de asientos
  state              String // Estado
  municipality       String // Municipio
  brand              String // Marca
  model              String // Modelo
  year               Int // Año
  color              String? // Color
  chassisNumber      String?              @unique // Número de chasis
  engineNumber       String?              @unique // Número de motor
  documentUrl        String? // URL del documento
  userId             String // Relación con el usuario
  User               User                 @relation(fields: [userId], references: [id]) // Relación con el usuario
  isActive           Boolean              @default(true)
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  DeclarationVehicle DeclarationVehicle[]
}

// Declaración de Vehículos
model DeclarationVehicle {
  id          String   @id @default(uuid())
  taxpayerId  String // Relación con el contribuyente
  taxpayer    Taxpayer @relation(fields: [taxpayerId], references: [id]) // Relación con el contribuyente
  VehicleId   String // Relación con el vehículo
  Vehicle     Vehicle  @relation(fields: [VehicleId], references: [id]) // Relación con el vehículo
  year        Int // Año de la declaración
  month       Int //1: Enero, 2: Febrero, etc.
  vehicleType String // Particular, Comercial, etc.
  taxRate     String?
  totalTax    Float?
  userId      String // Relación con el usuario
  User        User     @relation(fields: [userId], references: [id]) // Relación con el usuario
  status      String // Pendiente, En progreso, Completado, etc.
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

enum Role {
  USER
  ADMIN
}

enum OrderStatus {
  PENDING
  PROCESSING
  COMPLETED
  CANCELLED
}
