import express from 'express';
import { 
  getAllOrders, 
  getOrderById, 
  createOrder, 
  updateOrder, 
  deleteOrder,
  getUserOrders 
} from '../controllers/order.controller';
import { authenticate, authorize } from '../middleware/auth.middleware';

const router = express.Router();

/**
 * @route   GET /api/orders
 * @desc    Get all orders
 * @access  Private/Admin
 */
router.get('/', authenticate, authorize(['ADMIN']), getAllOrders);

/**
 * @route   GET /api/orders/user
 * @desc    Get current user's orders
 * @access  Private
 */
router.get('/user', authenticate, getUserOrders);

/**
 * @route   GET /api/orders/:id
 * @desc    Get order by ID
 * @access  Private
 */
router.get('/:id', authenticate, getOrderById);

/**
 * @route   POST /api/orders
 * @desc    Create a new order
 * @access  Private
 */
router.post('/', authenticate, createOrder);

/**
 * @route   PUT /api/orders/:id
 * @desc    Update order
 * @access  Private/Admin
 */
router.put('/:id', authenticate, authorize(['ADMIN']), updateOrder);

/**
 * @route   DELETE /api/orders/:id
 * @desc    Delete order
 * @access  Private/Admin
 */
router.delete('/:id', authenticate, authorize(['ADMIN']), deleteOrder);

export default router;
