import { Request, Response, NextFunction } from 'express';
import { prisma } from '../index';
import { logger } from '../utils/logger';

/**
 * Get all products
 * @route GET /api/products
 */
export const getAllProducts = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const products = await prisma.product.findMany({
      where: {
        isActive: true
      }
    });

    res.status(200).json({
      success: true,
      count: products.length,
      data: products
    });
  } catch (error) {
    logger.error('Error in getAllProducts controller:', error);
    next(error);
  }
};

/**
 * Get product by ID
 * @route GET /api/products/:id
 */
export const getProductById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const product = await prisma.product.findUnique({
      where: { id }
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.status(200).json({
      success: true,
      data: product
    });
  } catch (error) {
    logger.error('Error in getProductById controller:', error);
    next(error);
  }
};

/**
 * Create a new product
 * @route POST /api/products
 */
export const createProduct = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { name, description, price, stock } = req.body;

    const product = await prisma.product.create({
      data: {
        name,
        description,
        price: parseFloat(price),
        stock: parseInt(stock, 10) || 0
      }
    });

    res.status(201).json({
      success: true,
      data: product
    });
  } catch (error) {
    logger.error('Error in createProduct controller:', error);
    next(error);
  }
};

/**
 * Update product
 * @route PUT /api/products/:id
 */
export const updateProduct = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const { name, description, price, stock, isActive } = req.body;

    // Check if product exists
    const productExists = await prisma.product.findUnique({
      where: { id }
    });

    if (!productExists) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Prepare update data
    const updateData: any = {};
    
    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (price !== undefined) updateData.price = parseFloat(price);
    if (stock !== undefined) updateData.stock = parseInt(stock, 10);
    if (isActive !== undefined) updateData.isActive = isActive;

    // Update product
    const updatedProduct = await prisma.product.update({
      where: { id },
      data: updateData
    });

    res.status(200).json({
      success: true,
      data: updatedProduct
    });
  } catch (error) {
    logger.error('Error in updateProduct controller:', error);
    next(error);
  }
};

/**
 * Delete product
 * @route DELETE /api/products/:id
 */
export const deleteProduct = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    // Check if product exists
    const productExists = await prisma.product.findUnique({
      where: { id }
    });

    if (!productExists) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Delete product (soft delete by setting isActive to false)
    const deletedProduct = await prisma.product.update({
      where: { id },
      data: { isActive: false }
    });

    res.status(200).json({
      success: true,
      message: 'Product deleted successfully',
      data: deletedProduct
    });
  } catch (error) {
    logger.error('Error in deleteProduct controller:', error);
    next(error);
  }
};
