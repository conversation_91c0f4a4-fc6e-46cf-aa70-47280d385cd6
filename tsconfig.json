{"compilerOptions": {"target": "es2018", "module": "commonjs", "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "noImplicitAny": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noEmitOnError": false, "allowUnreachableCode": true, "allowUnusedLabels": true}, "ts-node": {"transpileOnly": true, "files": true}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts"]}