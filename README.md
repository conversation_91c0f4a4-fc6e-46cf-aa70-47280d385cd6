# Taxia Core API

Taxia Core es una API RESTful construida con Express.js y Prisma ORM, diseñada para proporcionar un backend robusto y escalable para aplicaciones de gestión.

## Características

- **Autenticación y Autorización**: Sistema completo de autenticación basado en JWT con roles de usuario.
- **Base de datos**: Integración con PostgreSQL a través de Prisma ORM.
- **API RESTful**: Endpoints bien estructurados siguiendo las mejores prácticas REST.
- **Validación**: Validación de datos de entrada para garantizar la integridad de los datos.
- **Logging**: Sistema de logging avanzado con Winston.
- **Seguridad**: Implementación de medidas de seguridad con Helmet.
- **Documentación**: API bien documentada con comentarios claros.

## Requisitos previos

- Node.js (v16 o superior)
- PostgreSQL (v12 o superior)
- npm o yarn

## Instalación

### 1. Clonar el repositorio

```bash
git clone https://github.com/tu-usuario/taxia-core.git
cd taxia-core
```

### 2. Instalar dependencias

```bash
npm install
```

### 3. Configurar variables de entorno

Crea un archivo `.env` en la raíz del proyecto con el siguiente contenido:

```
# Environment variables for the application
NODE_ENV=development
PORT=3000

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=1d

# Database Configuration
DATABASE_URL="postgresql://user:password@localhost:5432/taxia?schema=public"

# Logging Configuration
LOG_LEVEL=debug
```

Asegúrate de reemplazar los valores con tus propias configuraciones.

### 4. Configurar la base de datos

Asegúrate de que PostgreSQL esté en ejecución y crea una base de datos llamada `taxia`:

```bash
createdb taxia
```

### 5. Ejecutar migraciones de Prisma

```bash
npm run prisma:migrate
```

### 6. Generar el cliente de Prisma

```bash
npm run prisma:generate
```

## Ejecución

### Modo desarrollo

```bash
npm run dev
```

La API estará disponible en `http://localhost:3000`.

### Modo producción

```bash
npm run build
npm start
```

## Estructura del proyecto

```
taxia-core/
├── prisma/                 # Configuración y esquemas de Prisma
│   └── schema.prisma       # Definición del esquema de la base de datos
├── src/                    # Código fuente
│   ├── controllers/        # Controladores de la API
│   ├── middleware/         # Middleware personalizado
│   ├── models/             # Modelos y lógica de negocio
│   ├── routes/             # Definición de rutas
│   ├── utils/              # Utilidades y helpers
│   └── index.ts            # Punto de entrada de la aplicación
├── .env                    # Variables de entorno
├── package.json            # Dependencias y scripts
└── tsconfig.json           # Configuración de TypeScript
```

## API Endpoints

### Autenticación

- `POST /api/auth/register` - Registrar un nuevo usuario
- `POST /api/auth/login` - Iniciar sesión y obtener token JWT

### Usuarios

- `GET /api/users` - Obtener todos los usuarios (Admin)
- `GET /api/users/:id` - Obtener usuario por ID
- `POST /api/users` - Crear un nuevo usuario (Admin)
- `PUT /api/users/:id` - Actualizar usuario
- `DELETE /api/users/:id` - Eliminar usuario (Admin)

### Productos

- `GET /api/products` - Obtener todos los productos
- `GET /api/products/:id` - Obtener producto por ID
- `POST /api/products` - Crear un nuevo producto (Admin)
- `PUT /api/products/:id` - Actualizar producto (Admin)
- `DELETE /api/products/:id` - Eliminar producto (Admin)

### Órdenes

- `GET /api/orders` - Obtener todas las órdenes (Admin)
- `GET /api/orders/user` - Obtener órdenes del usuario actual
- `GET /api/orders/:id` - Obtener orden por ID
- `POST /api/orders` - Crear una nueva orden
- `PUT /api/orders/:id` - Actualizar estado de la orden (Admin)
- `DELETE /api/orders/:id` - Eliminar orden (Admin)

## Herramientas de desarrollo

### Prisma Studio

Para explorar y manipular los datos de la base de datos a través de una interfaz gráfica:

```bash
npm run prisma:studio
```

Prisma Studio estará disponible en `http://localhost:5555`.

## Despliegue

### Preparación para producción

1. Construye la aplicación:

```bash
npm run build
```

2. Configura las variables de entorno para producción:

```
NODE_ENV=production
PORT=3000
JWT_SECRET=your_secure_jwt_secret
JWT_EXPIRES_IN=1d
DATABASE_URL=your_production_database_url
LOG_LEVEL=warn
```

3. Ejecuta las migraciones en la base de datos de producción:

```bash
NODE_ENV=production npm run prisma:migrate
```

4. Inicia el servidor:

```bash
npm start
```

## Contribución

1. Haz un fork del repositorio
2. Crea una rama para tu feature (`git checkout -b feature/amazing-feature`)
3. Haz commit de tus cambios (`git commit -m 'Add some amazing feature'`)
4. Haz push a la rama (`git push origin feature/amazing-feature`)
5. Abre un Pull Request

## Licencia

Este proyecto está licenciado bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para más detalles.
