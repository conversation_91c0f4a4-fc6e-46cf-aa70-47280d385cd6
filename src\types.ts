/**
 * TAXIA - Sistema de Gestión Tributaria Municipal
 * Archivo consolidado de tipos TypeScript para todos los módulos
 * 
 * Este archivo contiene todas las interfaces y tipos necesarios para:
 * - Autenticación y usuarios
 * - Contribuyentes (Taxpayers)
 * - Módulos de registro tributario
 * - Envío simulado de datos
 */

// ============================================================================
// TIPOS BASE Y AUTENTICACIÓN
// ============================================================================

/**
 * Interface para usuarios del sistema (autenticación)
 */
export interface User {
    id: string;
    username?: string;
    email: string;
    emailVerified?: boolean;
    taxpayerId?: string;
    password?: string;
    avatarUrl?: string;
    firstname?: string;
    lastname?: string;
    phone?: string;
    provider?: string;
    role: string;
}

/**
 * Interface para contribuyentes/taxpayers
 * Información básica del contribuyente que se usa en todos los módulos
 */
export interface Taxpayer {
    rif: string;              // RIF del contribuyente
    name: string;              // Nombre o razón social
    licenseNumber: string;     // Número de licencia/cuenta
    email: string;            // Email de contacto
    legalResponsible?: string; // Responsable legal (opcional)
    localPhone?: string;       // Teléfono local (opcional)
    phone: string;            // Teléfono principal
    address: string;          // Dirección
    location?: Location;      // Ubicación geográfica (opcional)
    state: string;            // Estado
    municipality: string;     // Municipio
}

/**
 * Interface para ubicación geográfica
 */
export interface Location {
    latitude: number;
    longitude: number;
    address?: string;
}

/**
 * Tipos de declaración comunes
 */
export type DeclarationType = 'definitiva' | 'sustitutiva' | 'anticipada';
export type DeclarationStatus = 'enviada' | 'pendiente' | 'rechazada' | 'aprobada' | 'pagado';

/**
 * Interface base para períodos de declaración
 */
export interface DeclarationPeriod {
    type: string;    // Tipo de período (mensual, trimestral, anual, etc.)
    year: number;    // Año
    month?: number;  // Mes (opcional, para declaraciones mensuales)
    quarter?: string; // Trimestre (opcional, para declaraciones trimestrales)
}

// ============================================================================
// MÓDULO: ACTIVIDADES ECONÓMICAS
// ============================================================================

/**
 * Interface para actividades económicas
 */
export interface EconomicActivity {
    number: string;   // Número de la actividad
    code: string;     // Código de la actividad
    descri: string;   // Descripción de la actividad
    alicuota: string; // Alícuota aplicable
    mmv: number;      // Monto en MMV
}

/**
 * Datos del formulario de registro de actividades económicas
 */
export interface EconomicActivityFormData {
    taxpayerId: string;
    declaration: {
        type: DeclarationType;
        year: number;
        month: number;
    };
    location: Location | null;
    selectedActivity: EconomicActivity | null;
    ivaDeclaration: File | null;           // Declaración de IVA
    ivaRelationDeclaration: File | null;   // Relación de declaración de IVA
}

/**
 * Interface para declaraciones de actividades económicas (listado)
 */
export interface EconomicActivityDeclaration {
    id: string;
    declarationType: DeclarationType;
    year: number;
    month: number;
    total: number;
    submissionDate: string;
    status: DeclarationStatus;
    activities: EconomicActivity[];
}

// ============================================================================
// MÓDULO: INMUEBLES URBANOS
// ============================================================================

/**
 * Entrada de propiedad para inmuebles urbanos
 * Usada en: UrbanRealEstateRegistration.tsx para manejar múltiples propiedades
 * y en UrbanRealEstateConfirmationModal.tsx para mostrar el resumen
 */
export interface PropertyEntry {
    id: string;
    propertyType: string;        // Tipo de propiedad (terreno/construcción)
    propertyTypeName: string;    // Nombre descriptivo del tipo
    description: string;         // Descripción de la propiedad
    usage: string;              // Uso de la propiedad
    sectorClassification: string; // Clasificación de sector
    landValuePlan: string;       // Plan de valores de tierra
    landArea: number;           // Área de terreno (m²)
    constructionArea: number;   // Área de construcción (m²)
    tcmdM2: number;            // TCMD/m²
    exchangeRateType: string;   // Tipo de cambio (MMV)
    aliquot: number;           // Alícuota
    taxAmount: number;         // Monto del impuesto (Bs.)
}

/**
 * Datos del formulario de registro de inmuebles urbanos
 */
export interface UrbanRealEstateFormData {
    taxpayerId: string;
    declaration: {
        paymentScheme: string;  // Esquema de pago (Anual, Trimestral)
        period: string;         // Período de la declaración
        year: number;
    };
    propertyDetails: {
        propertyType: string;
        description: string;
        usage: string;
        sectorClassification: string;
        landValuePlan: string;
        constructionValuePlan: string;
        constructionType: string;
        landArea: number;
        constructionArea: number;
        tcmdM2: number;
        exchangeRateType: string;
        aliquot: number;
        taxAmount: number;
    };
    documents: {
        propertyDocument: File | null;              // Documento de propiedad
        administrativeFeeDocument: File | null;     // Constancia de tasa administrativa
    };
}

/**
 * Interface para declaraciones de inmuebles urbanos (listado)
 */
export interface UrbanRealEstateDeclaration {
    id: string;
    location: string;
    state: string;
    municipality: string;
    cadastralId: string;
    area: number;
    propertyType: string;
    propertyTypeName: string;
    description: string;
    usage: string;
    landValuePlan: string;
    constructionValuePlan: string;
    constructionType: string;
    zone: string;
    landArea: number;
    constructionArea: number;
    tcmdM2: number;
    exchangeRateType: string;
    aliquot: number;
    taxAmount: number;
    period: string;
    year: number;
    submissionDate: string;
    status: DeclarationStatus;
}

// ============================================================================
// MÓDULO: VEHÍCULOS
// ============================================================================

/**
 * Interface para vehículos
 */
export interface Vehicle {
    id: string;
    plateNumber: string;    // Número de placa
    brand: string;          // Marca
    model: string;          // Modelo
    ownerName: string;      // Nombre del propietario
    year: number;           // Año del vehículo
    registrationDate: string; // Fecha de registro
}

/**
 * Datos del formulario de registro de vehículos
 */
export interface VehicleFormData {
    taxpayerId: string;
    vehicleDetails: {
        plateNumber: string;
        brand: string;
        model: string;
        year: number;
        vehicleType: string;    // Tipo de vehículo
        engineNumber: string;   // Número de motor
        chassisNumber: string;  // Número de chasis
        color: string;          // Color
    };
    documents: {
        registrationDocument: File | null;  // Documento de registro
        ownershipDocument: File | null;     // Documento de propiedad
    };
}

/**
 * Interface para declaraciones de vehículos (listado)
 */
export interface VehicleDeclaration {
    id: string;
    plateNumber: string;
    brand: string;
    model: string;
    year: number;
    vehicleType: string;
    ownerName: string;
    taxAmount: number;
    period: string;
    submissionDate: string;
    status: DeclarationStatus;
}

// ============================================================================
// MÓDULO: APUESTAS LÍCITAS
// ============================================================================

/**
 * Entrada de apuesta para apuestas lícitas
 */
export interface BetEntry {
    id: string;
    betType: string;         // Tipo de apuesta
    betTypeName: string;     // Nombre descriptivo del tipo
    numberOfBets: number;    // Número de apuestas
    betAmount: number;       // Monto de la apuesta
    taxRate: string;         // Tasa de impuesto
    taxAmount: number;       // Monto del impuesto
    declarationType: string; // Tipo de declaración
    month: number;
    year: number;
}

/**
 * Datos del formulario de registro de apuestas lícitas
 */
export interface LegalBetFormData {
    taxpayerId: string;
    declaration: {
        type: string;    // Esquema de pago (Diario, Mensual, Semestral, Anual)
        month: number;
        year: number;
    };
    betDetails: {
        betType: string;
        betNumber: string;
        betAmount: number;
        taxRate: string;
        taxAmount: number;
    };
}

/**
 * Interface para declaraciones de apuestas lícitas (listado)
 */
export interface LegalBetDeclaration {
    id: string;
    betType: string;
    numberOfBets: number;
    betAmount: number;
    taxRate: string;
    total: number;
    month: number;
    year: number;
    submissionDate: string;
    status: DeclarationStatus;
    entries: BetEntry[];
}

// ============================================================================
// MÓDULO: PUBLICIDAD Y PROPAGANDA
// ============================================================================

/**
 * Entrada de publicidad para publicidad y propaganda
 */
export interface AdvertisingEntry {
    id: string;
    type: string;
    exactAddress: string;
    area: number;
    numberOfElements: number;
    numberOfDays: number;
    fraction: number;
    exchangeType: number;
    // advertisingType: string;     // Tipo de publicidad
    // advertisingTypeName: string; // Nombre descriptivo del tipo
    description: string;         // Descripción
    location?: string;           // Ubicación
    dimensions?: string;         // Dimensiones
    duration?: number;           // Duración (días)
    // taxRate: string;           // Tasa de impuesto
    taxAmount: number;         // Monto del impuesto
    // declarationType: string;    // Tipo de declaración
    month: number;
    year: number;
}

/**
 * Datos del formulario de registro de publicidad
 */
export interface AdvertisingFormData {
    taxpayerId: string;
    declaration: DeclarationPeriod;
    advertisingDetails: {
        advertisingType: string;     // Tipo de publicidad
        description: string;         // Descripción
        location: string;           // Ubicación
        dimensions: string;         // Dimensiones
        duration: number;           // Duración (días)
        taxRate: string;           // Tasa de impuesto
        taxAmount: number;         // Monto del impuesto
    };
    documents: {
        advertisingPermit: File | null;    // Permiso de publicidad
        locationDocument: File | null;     // Documento de ubicación
    };
}

/**
 * Interface para declaraciones de publicidad (listado)
 */
export interface AdvertisingDeclaration {
    id: string;
    advertisingType: string;
    description: string;
    // location: string;
    // dimensions: string;
    // duration: number;
    // taxRate: string;
    total: number;
    month: number;
    year: number;
    submissionDate: string;
    status: DeclarationStatus;
    entries: AdvertisingEntry[];
}

// ============================================================================
// MÓDULO: ESPECTÁCULOS PÚBLICOS
// ============================================================================

/**
 * Entrada de espectáculo para espectáculos públicos
 */
export interface ShowEntry {
    id: string;
    showType: string;          // Tipo de espectáculo
    showTypeName: string;      // Nombre descriptivo del tipo
    showName: string;          // Nombre del espectáculo
    venue: string;             // Lugar del evento
    eventDate: string;         // Fecha del evento
    capacity: number;          // Capacidad
    ticketPrice: number;       // Precio de entrada
    taxRate: string;          // Tasa de impuesto
    taxAmount: number;        // Monto del impuesto
    declarationType: string;   // Tipo de declaración
    month: number;
    year: number;
}

/**
 * Datos del formulario de registro de espectáculos públicos
 */
export interface ShowFormData {
    taxpayerId: string;
    declaration: DeclarationPeriod;
    showDetails: {
        showType: string;          // Tipo de espectáculo
        showName: string;          // Nombre del espectáculo
        venue: string;             // Lugar del evento
        eventDate: string;         // Fecha del evento
        capacity: number;          // Capacidad
        ticketPrice: number;       // Precio de entrada
        taxRate: string;          // Tasa de impuesto
        taxAmount: number;        // Monto del impuesto
    };
    documents: {
        eventPermit: File | null;        // Permiso del evento
        venueDocument: File | null;      // Documento del lugar
    };
}

/**
 * Interface para declaraciones de espectáculos públicos (listado)
 */
export interface ShowDeclaration {
    id: string;
    showType: string;
    showName: string;
    venue: string;
    eventDate: string;
    capacity: number;
    ticketPrice: number;
    taxRate: string;
    total: number;
    month: number;
    year: number;
    submissionDate: string;
    status: DeclarationStatus;
    entries: ShowEntry[];
}

// ============================================================================
// MÓDULO: TASAS (TAX RATES)
// ============================================================================

/**
 * Entrada de tasa para tasas municipales
 */
export interface TaxRateEntry {
    id: string;
    taxType: string;           // Tipo de tasa
    taxTypeName: string;       // Nombre descriptivo del tipo
    description: string;       // Descripción del servicio
    amount: number;           // Monto de la tasa
    taxRate: string;          // Tasa aplicable
    taxAmount: number;        // Monto del impuesto
    declarationType: string;   // Tipo de declaración
    month: number;
    year: number;
}

/**
 * Datos del formulario de registro de tasas
 */
export interface TaxRateFormData {
    taxpayerId: string;
    declaration: DeclarationPeriod;
    taxDetails: {
        taxType: string;          // Tipo de tasa
        description: string;      // Descripción del servicio
        amount: number;          // Monto base
        taxRate: string;         // Tasa aplicable
        taxAmount: number;       // Monto del impuesto
    };
    documents: {
        serviceRequest: File | null;     // Solicitud de servicio
        paymentProof: File | null;       // Comprobante de pago
    };
}

/**
 * Interface para declaraciones de tasas (listado)
 */
export interface TaxRateDeclaration {
    id: string;
    // taxType: string;
    // description: string;
    // amount: number;
    // taxRate: string;
    total: number;
    month: number;
    year: number;
    submissionDate: string;
    status: DeclarationStatus;
    numberOfTaxRates: number;
    selectedTaxRates: TaxRateItem[];
    entries?: TaxRateEntry[];
}

export interface TaxRateItem {
    id: string;
    taxType: string;
    description: string;
    calculationVariable: string;
    unitValue: number;
    quantity: number;
    totalAmount: number;
    isSelected: boolean;
}

// ============================================================================
// MÓDULO: ASEO URBANO
// ============================================================================

/**
 * Entrada de servicio para aseo urbano
 */
export interface SanitationEntry {
    id: string;
    taxpayerType: string;      // Tipo de contribuyente
    serviceType: string;       // Tipo de servicio
    serviceTypeName: string;   // Nombre descriptivo del tipo
    frequency: string;         // Frecuencia del servicio
    wasteVolume: number;       // Volumen de residuos
    taxRate: string;          // Tasa de impuesto
    taxAmount: number;        // Monto del impuesto
    declarationType: string;   // Tipo de declaración
    month: number;
    year: number;
}

/**
 * Datos del formulario de registro de aseo urbano
 */
export interface UrbanSanitationFormData {
    taxpayer: string;
    declaration: DeclarationPeriod;
    sanitationDetails: {
        taxpayerType: string;      // Tipo de contribuyente
        serviceType: string;       // Tipo de servicio
        frequency: string;         // Frecuencia del servicio
        wasteVolume: number;       // Volumen de residuos
        taxRate: string;          // Tasa de impuesto
        taxAmount: number;        // Monto del impuesto
    };
    documents: {
        serviceContract: File | null;    // Contrato de servicio
        wasteReport: File | null;        // Reporte de residuos
    };
}

/**
 * Interface para declaraciones de aseo urbano (listado)
 */
export interface UrbanSanitationDeclaration {
    id: string;
    // taxpayerType: string;
    // serviceType: string;
    category: string;
    zone: string;
    // frequency: string;
    // wasteVolume: number;
    // taxRate: string;
    total: number;
    month: number;
    year: number;
    submissionDate: string;
    status: string;
    entries?: SanitationEntry[];
    propertyInfo: PropertyInfo;
    taxpayer: string;
}

export interface PropertyInfo {
    location: string;
    cadastralCode: string;
    terrainArea: number;
    paymentPeriod: {
        year: number;
        month: number;
    };
    taxpayerType: string;
    licenseNumber: string;
    parish: string;
    zone: string;
    category: string;
    mmvAmount: number;
}

// ============================================================================
// INTERFACES PARA ENVÍO SIMULADO DE DATOS
// ============================================================================

/**
 * Interface base para respuesta de API
 */
export interface ApiResponse<T = any> {
    success: boolean;
    data: T;
    message: string;
    errors?: string[];
}

/**
 * Interface para respuesta de autenticación
 */
export interface AuthResponse {
    user: User;
    token: string;
    refreshToken: string;
    taxpayer?: Taxpayer;
}

/**
 * Interface para datos de envío simulado
 * Estructura común para todos los módulos al enviar datos al backend
 */
export interface SubmissionData {
    module: string;              // Nombre del módulo (ej: 'economic-activities')
    taxpayerId: string;          // Datos del contribuyente
    declaration: DeclarationPeriod; // Período de declaración
    formData: any;              // Datos específicos del formulario
    documents: File[];          // Archivos adjuntos
    calculatedTax: number;      // Impuesto calculado
    submissionDate: string;     // Fecha de envío
    metadata?: {                // Metadatos adicionales
        userAgent?: string;
        ipAddress?: string;
        sessionId?: string;
    };
}

/**
 * Interface para respuesta de envío
 */
export interface SubmissionResponse {
    submissionId: string;       // ID único de la declaración
    status: 'success' | 'error' | 'pending';
    message: string;
    receiptNumber?: string;     // Número de recibo (si aplica)
    paymentReference?: string;  // Referencia de pago (si aplica)
    errors?: string[];
}

// ============================================================================
// CONSTANTES Y ENUMS
// ============================================================================

/**
 * Meses del año
 */
export const MONTHS = [
    { value: 1, label: 'Enero' },
    { value: 2, label: 'Febrero' },
    { value: 3, label: 'Marzo' },
    { value: 4, label: 'Abril' },
    { value: 5, label: 'Mayo' },
    { value: 6, label: 'Junio' },
    { value: 7, label: 'Julio' },
    { value: 8, label: 'Agosto' },
    { value: 9, label: 'Septiembre' },
    { value: 10, label: 'Octubre' },
    { value: 11, label: 'Noviembre' },
    { value: 12, label: 'Diciembre' }
];

/**
 * Tipos de declaración disponibles
 */
export const DECLARATION_TYPES = [
    { value: 'definitiva', label: 'Definitiva' },
    { value: 'sustitutiva', label: 'Sustitutiva' },
    { value: 'anticipada', label: 'Anticipada' }
];

/**
 * Estados de declaración
 */
export const DECLARATION_STATUSES = [
    { value: 'enviada', label: 'Enviada' },
    { value: 'pendiente', label: 'Pendiente' },
    { value: 'rechazada', label: 'Rechazada' }
];

/**
 * Función utilitaria para generar años disponibles
 */
export const getAvailableYears = (): Array<{ value: number; label: string }> => {
    const currentYear = new Date().getFullYear();
    const years = [];

    for (let year = currentYear; year >= currentYear - 10; year--) {
        years.push({ value: year, label: year.toString() });
    }

    return years;
};

/**
 * Función utilitaria para formatear fechas
 */
export const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-VE', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
};
