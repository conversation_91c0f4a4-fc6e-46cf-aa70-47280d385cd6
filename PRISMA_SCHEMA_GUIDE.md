# Guía del Esquema de Prisma - TAXIA

## Descripción General

Este documento describe el esquema de base de datos de Prisma para el sistema TAXIA (Sistema de Gestión Tributaria Municipal), basado en los tipos definidos en `src/types.ts`.

## Estructura de la Base de Datos

### 🔐 Modelos Base - Autenticación

#### User
Modelo para usuarios del sistema con autenticación híbrida (local + Google).

```prisma
model User {
  id            String   @id @default(uuid())
  username      String?  @unique
  email         String   @unique
  emailVerified Boolean  @default(false)
  password      String?
  avatarUrl     String?
  firstname     String?
  lastname      String?
  phone         String?
  provider      String   @default("local") // 'local' o 'google'
  providerId    String?  // UID de Firebase
  role          String   @default("USER")
  taxpayerId    String?  @unique
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relación con Taxpayer
  taxpayer Taxpayer? @relation(fields: [taxpayerId], references: [id])
}
```

#### Taxpayer
Modelo para contribuyentes/taxpayers con información fiscal.

```prisma
model Taxpayer {
  id               String    @id @default(uuid())
  rif              String    @unique
  name             String
  licenseNumber    String    @unique
  email            String
  legalResponsible String?
  localPhone       String?
  phone            String
  address          String
  state            String
  municipality     String
  latitude         Float?
  longitude        Float?
  locationAddress  String?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
}
```

### 📊 Modelos de Declaraciones

El sistema maneja 7 tipos principales de declaraciones tributarias:

#### 1. Actividades Económicas
- `EconomicActivityDeclaration`: Declaración principal
- `EconomicActivityEntry`: Entradas individuales de actividades

#### 2. Inmuebles Urbanos
- `UrbanRealEstateDeclaration`: Declaración de propiedades
- `PropertyEntry`: Entradas individuales de propiedades

#### 3. Vehículos
- `VehicleDeclaration`: Declaración de vehículos
- `VehicleEntry`: Entradas individuales de vehículos

#### 4. Apuestas Lícitas
- `LegalBetDeclaration`: Declaración de apuestas
- `BetEntry`: Entradas individuales de apuestas

#### 5. Publicidad y Propaganda
- `AdvertisingDeclaration`: Declaración de publicidad
- `AdvertisingEntry`: Entradas individuales de publicidad

#### 6. Espectáculos Públicos
- `ShowDeclaration`: Declaración de espectáculos
- `ShowEntry`: Entradas individuales de espectáculos

#### 7. Tasas Municipales
- `TaxRateDeclaration`: Declaración de tasas
- `TaxRateEntry`: Entradas individuales de tasas
- `TaxRateItem`: Items seleccionados de tasas

#### 8. Aseo Urbano
- `UrbanSanitationDeclaration`: Declaración de aseo urbano
- `SanitationEntry`: Entradas individuales de servicios
- `PropertyInfo`: Información de propiedades para aseo

## Relaciones Principales

### Usuario → Contribuyente
- Relación 1:1 opcional entre `User` y `Taxpayer`
- Un usuario puede tener un contribuyente asociado
- Un contribuyente puede tener múltiples usuarios

### Contribuyente → Declaraciones
- Relación 1:N entre `Taxpayer` y cada tipo de declaración
- Un contribuyente puede tener múltiples declaraciones de cada tipo

### Declaración → Entradas
- Relación 1:N entre cada declaración y sus entradas
- Cada declaración puede tener múltiples entradas/items

## Estados de Declaración

Todos los modelos de declaración incluyen un campo `status` con los siguientes valores:
- `"pendiente"` - Estado inicial
- `"enviada"` - Declaración enviada
- `"rechazada"` - Declaración rechazada
- `"aprobada"` - Declaración aprobada
- `"pagado"` - Declaración pagada

## Tipos de Declaración

Campo `declarationType` disponible en varios modelos:
- `"definitiva"` - Declaración definitiva
- `"sustitutiva"` - Declaración sustitutiva
- `"anticipada"` - Declaración anticipada

## Comandos de Prisma

### Generar Cliente
```bash
npm run prisma:generate
```

### Crear Migración
```bash
npm run prisma:migrate
```

### Validar Esquema
```bash
npm run prisma:validate
```

### Reset Base de Datos
```bash
npm run prisma:reset
```

### Abrir Prisma Studio
```bash
npm run prisma:studio
```

## Variables de Entorno Requeridas

```env
DATABASE_URL="postgresql://username:password@localhost:5432/taxia_db"
```

## Consideraciones de Diseño

1. **UUIDs**: Todos los modelos usan UUIDs como claves primarias para mayor seguridad
2. **Timestamps**: Todos los modelos incluyen `createdAt` y `updatedAt`
3. **Cascada**: Las entradas se eliminan automáticamente cuando se elimina la declaración padre
4. **Índices**: Campos únicos como `rif`, `licenseNumber`, `email` tienen índices automáticos
5. **Opcionales**: Muchos campos son opcionales para flexibilidad en el registro

## Próximos Pasos

1. Configurar la base de datos PostgreSQL
2. Ejecutar `npm run prisma:migrate` para crear las tablas
3. Ejecutar `npm run prisma:generate` para generar el cliente
4. Actualizar los controladores para usar los nuevos modelos
