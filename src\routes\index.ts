import express from 'express';
import userRoutes from './user.routes';
// import productRoutes from './product.routes';
// import orderRoutes from './order.routes';
import authRoutes from './auth.routes';

const router = express.Router();

// API information route
router.get('/', (req, res) => {
  res.json({
    message: 'Welcome to Taxia Core API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      // products: '/api/products',
      // orders: '/api/orders'
    }
  });
});

// Mount routes
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
// router.use('/products', productRoutes);
// router.use('/orders', orderRoutes);

export default router;
