import { Request, Response, NextFunction } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { prisma } from '../index';
import { logger } from '../utils/logger';
import { verifyFirebaseToken, FirebaseUserData } from '../utils/firebase.utils';

/**
 * Register a new user
 * @route POST /api/auth/register
 */
export const register = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { firstname, lastname, email, password } = req.body;

    // Check if user already exists
    const userExists = await prisma.user.findUnique({
      where: { email }
    });

    if (userExists) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user
    const user = await prisma.user.create({
      data: {
        firstname,
        lastname,
        email,
        password: hashedPassword
      }
    });

    // Generate JWT tokens
    const { accessToken, refreshToken } = generateJWT(user.id, user.email, user.role);

    res.status(201).json({
      message: 'User registered successfully',
      token: accessToken,
      refreshToken,
      user: {
        id: user.id,
        firstname: user.firstname,
        lastname: user.lastname,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    logger.error('Error in register controller:', error);
    next(error);
  }
};

/**
 * Hybrid login user - supports both local and Firebase authentication
 * @route POST /api/auth/login
 */
export const login = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email, password, firebaseToken } = req.body;

    // Firebase authentication flow
    if (firebaseToken) {
      return await handleFirebaseLogin(firebaseToken, res, next);
    }

    // Local authentication flow
    if (!email || !password) {
      return res.status(400).json({
        message: 'Email and password are required for local authentication'
      });
    }

    return await handleLocalLogin(email, password, res, next);
  } catch (error) {
    logger.error('Error in login controller:', error);
    next(error);
  }
};

/**
 * Handle local user authentication
 */
const handleLocalLogin = async (email: string, password: string, res: Response, next: NextFunction) => {
  try {
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Check if user is a local user (has password)
    if (!user.password) {
      return res.status(401).json({
        message: 'This account was created with Google. Please use Google Sign-In.'
      });
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Get taxpayer info
    const taxpayer = await prisma.taxpayer.findUnique({
      where: { id: user.id }
    });

    // Generate JWT tokens
    const { accessToken, refreshToken } = generateJWT(user.id, user.email, user.role);

    res.status(200).json({
      message: 'Login successful',
      token: accessToken,
      refreshToken,
      user: {
        id: user.id,
        firstname: user.firstname,
        lastname: user.lastname,
        email: user.email,
        role: user.role,
        provider: user.provider,
        avatarUrl: user.avatarUrl
      },
      taxpayer: taxpayer
    });
  } catch (error) {
    logger.error('Error in handleLocalLogin:', error);
    throw error;
  }
};

/**
 * Handle Firebase user authentication
 */
const handleFirebaseLogin = async (firebaseToken: string, res: Response, next: NextFunction) => {
  try {
    // Verify Firebase token
    const firebaseUser = await verifyFirebaseToken(firebaseToken);

    if (!firebaseUser) {
      return res.status(401).json({ message: 'Invalid Firebase token' });
    }

    // Check if user exists in our database
    let user = await prisma.user.findUnique({
      where: { email: firebaseUser.email }
    });

    if (!user) {
      return res.status(404).json({
        message: 'User not found. Please register first.',
        firebaseUser: {
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          name: firebaseUser.name,
          picture: firebaseUser.picture
        }
      });
    }

    // Verify that this is a Google user
    if (user.provider !== 'google') {
      return res.status(401).json({
        message: 'This email is associated with a local account. Please use email and password.'
      });
    }

    // Get taxpayer info
    const taxpayer = await prisma.taxpayer.findUnique({
      where: { id: user.id }
    });

    // Generate JWT tokens
    const { accessToken, refreshToken } = generateJWT(user.id, user.email, user.role);

    res.status(200).json({
      message: 'Login successful',
      token: accessToken,
      refreshToken,
      user: {
        id: user.id,
        firstname: user.firstname,
        lastname: user.lastname,
        email: user.email,
        role: user.role,
        provider: user.provider,
        avatarUrl: user.avatarUrl
      },
      taxpayer: taxpayer
    });
  } catch (error) {
    logger.error('Error in handleFirebaseLogin:', error);
    throw error;
  }
};

/**
 * Generate JWT tokens (access token and refresh token)
 */
const generateJWT = (id: string, email: string, role: string) => {
  const accessToken = jwt.sign(
    { id, email, role, type: 'access' },
    process.env.JWT_SECRET || 'default_secret',
    { expiresIn: '1h' } // Access token expires in 1 hour
  );

  const refreshToken = jwt.sign(
    { id, email, type: 'refresh' },
    process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET || 'default_refresh_secret',
    { expiresIn: '30d' } // Refresh token expires in 30 days
  );

  return { accessToken, refreshToken };
};

/**
 * Refresh access token using refresh token
 * @route POST /api/auth/refresh
 */
export const refreshToken = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      return res.status(400).json({ message: 'Refresh token is required' });
    }

    // Verify refresh token
    const decoded = jwt.verify(
      refreshToken,
      process.env.JWT_REFRESH_SECRET || process.env.JWT_SECRET || 'default_refresh_secret'
    ) as jwt.JwtPayload;

    // Check if it's a refresh token
    if (!decoded || decoded.type !== 'refresh') {
      return res.status(401).json({ message: 'Invalid refresh token' });
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: decoded.id as string }
    });

    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = generateJWT(user.id, user.email, user.role);

    res.status(200).json({
      message: 'Token refreshed successfully',
      token: accessToken,
      refreshToken: newRefreshToken
    });
  } catch (error) {
    logger.error('Error in refreshToken controller:', error);

    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({ message: 'Invalid refresh token' });
    }

    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({ message: 'Refresh token expired' });
    }

    next(error);
  }
};

/**
 * Google authentication - handles both login and registration
 * @route POST /api/auth/google
 */
export const googleAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { idToken } = req.body;

    if (!idToken) {
      return res.status(400).json({ message: 'Firebase ID token is required' });
    }

    // Verify Firebase token
    const firebaseUser = await verifyFirebaseToken(idToken);

    if (!firebaseUser) {
      return res.status(401).json({ message: 'Invalid Firebase token' });
    }

    // Check if user already exists
    let user = await prisma.user.findUnique({
      where: { email: firebaseUser.email }
    });

    if (user) {
      // User exists - login flow
      if (user.provider !== 'google') {
        return res.status(400).json({
          message: 'This email is associated with a local account. Please use email and password to login.'
        });
      }

      // Update user info from Firebase (in case profile was updated)
      const nameParts = firebaseUser.name?.split(' ') || [];
      const firstname = nameParts[0] || user.firstname;
      const lastname = nameParts.slice(1).join(' ') || user.lastname;

      user = await prisma.user.update({
        where: { id: user.id },
        data: {
          firstname,
          lastname,
          avatarUrl: firebaseUser.picture || user.avatarUrl,
          emailVerified: firebaseUser.emailVerified
        }
      });

      // Generate JWT tokens
      const { accessToken, refreshToken } = generateJWT(user.id, user.email, user.role);

      res.status(200).json({
        message: 'Login successful',
        token: accessToken,
        refreshToken,
        user: {
          id: user.id,
          firstname: user.firstname,
          lastname: user.lastname,
          email: user.email,
          role: user.role,
          provider: user.provider,
          avatarUrl: user.avatarUrl,
          emailVerified: user.emailVerified
        }
      });
    } else {
      // User doesn't exist - registration flow
      const nameParts = firebaseUser.name?.split(' ') || [];
      const firstname = nameParts[0] || '';
      const lastname = nameParts.slice(1).join(' ') || '';

      // Create Google user
      user = await prisma.user.create({
        data: {
          email: firebaseUser.email,
          firstname,
          lastname,
          provider: 'google',
          providerId: firebaseUser.uid,
          emailVerified: firebaseUser.emailVerified,
          avatarUrl: firebaseUser.picture,
          password: null // Google users don't have passwords
        }
      });

      // Generate JWT tokens
      const { accessToken, refreshToken } = generateJWT(user.id, user.email, user.role);

      res.status(201).json({
        message: 'User registered and logged in successfully',
        token: accessToken,
        refreshToken,
        user: {
          id: user.id,
          firstname: user.firstname,
          lastname: user.lastname,
          email: user.email,
          role: user.role,
          provider: user.provider,
          avatarUrl: user.avatarUrl,
          emailVerified: user.emailVerified
        }
      });
    }
  } catch (error) {
    logger.error('Error in googleAuth controller:', error);
    next(error);
  }
};
