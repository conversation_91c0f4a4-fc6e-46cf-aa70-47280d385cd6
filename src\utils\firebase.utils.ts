import { admin } from '../config/firebase.config';
import { logger } from './logger';

export interface FirebaseUserData {
  uid: string;
  email: string;
  name?: string;
  picture?: string;
  emailVerified: boolean;
}

/**
 * Verify Firebase ID token and extract user data
 * @param idToken - Firebase ID token from the client
 * @returns Promise<FirebaseUserData | null>
 */
export const verifyFirebaseToken = async (idToken: string): Promise<FirebaseUserData | null> => {
  try {
    // Check if Firebase is initialized
    if (admin.apps.length === 0) {
      logger.error('Firebase Admin SDK is not initialized');
      return null;
    }

    // Verify the ID token
    const decodedToken = await admin.auth().verifyIdToken(idToken);

    logger.info('Firebase token verified successfully for user:', decodedToken.uid);

    // Extract user data from the decoded token
    const userData: FirebaseUserData = {
      uid: decodedToken.uid,
      email: decodedToken.email || '',
      name: decodedToken.name,
      picture: decodedToken.picture,
      emailVerified: decodedToken.email_verified || false
    };

    return userData;
  } catch (error) {
    logger.error('Error verifying Firebase token:', error);
    return null;
  }
};

/**
 * Get user data from Firebase by UID
 * @param uid - Firebase user UID
 * @returns Promise<FirebaseUserData | null>
 */
export const getFirebaseUser = async (uid: string): Promise<FirebaseUserData | null> => {
  try {
    // Check if Firebase is initialized
    if (admin.apps.length === 0) {
      logger.error('Firebase Admin SDK is not initialized');
      return null;
    }

    const userRecord = await admin.auth().getUser(uid);

    const userData: FirebaseUserData = {
      uid: userRecord.uid,
      email: userRecord.email || '',
      name: userRecord.displayName,
      picture: userRecord.photoURL,
      emailVerified: userRecord.emailVerified
    };

    return userData;
  } catch (error) {
    logger.error('Error getting Firebase user:', error);
    return null;
  }
};

/**
 * Check if a Firebase user exists by email
 * @param email - User email
 * @returns Promise<boolean>
 */
export const firebaseUserExistsByEmail = async (email: string): Promise<boolean> => {
  try {
    // Check if Firebase is initialized
    if (admin.apps.length === 0) {
      logger.error('Firebase Admin SDK is not initialized');
      return false;
    }

    await admin.auth().getUserByEmail(email);
    return true;
  } catch (error) {
    // User not found
    return false;
  }
};
