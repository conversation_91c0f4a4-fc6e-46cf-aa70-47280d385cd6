import express from 'express';
import { login, register, googleAuth, refreshToken } from '../controllers/auth.controller';

const router = express.Router();

/**
 * @route   POST /api/auth/register
 * @desc    Register a new user
 * @access  Public
 */
router.post('/register', register);

/**
 * @route   POST /api/auth/login
 * @desc    Hybrid login - supports both local and Firebase authentication
 * @access  Public
 */
router.post('/login', login);

/**
 * @route   POST /api/auth/google
 * @desc    Google authentication - handles both login and registration
 * @access  Public
 */
router.post('/google', googleAuth);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token using refresh token
 * @access  Public
 */
router.post('/refresh', refreshToken);

export default router;
